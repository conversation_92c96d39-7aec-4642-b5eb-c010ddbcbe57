import { Injectable, Inject, Logger } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { ethers } from 'ethers';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { GetNonceDto, NonceResponseDto, Web3LoginDto, Web3LoginResponseDto } from './dto/get-nonce.dto';
import { User } from '../models/user.model';
import { UserWallet } from '../models/user-wallet.model';
import { generateUniqueCode } from '../utils/random.util';

@Injectable()
export class Web3AuthService {
  private readonly logger = new Logger(Web3AuthService.name);

  constructor(
    @Inject('REDIS_CLIENT') private readonly redisClient: any,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 生成用于签名的消息
   * @param nonce 随机数
   * @returns 签名消息
   */
  generateSignMessage(nonce: string): string {
    return `Welcome to MooFun!\n\nPlease sign to verify your wallet address, after which you can begin your wonderful journey.\n\nSecurity code: ${nonce}\nTimestamp: ${Date.now()}`;
  }

  /**
   * 检查Redis连接状态
   * @returns Redis连接是否正常
   */
  async checkRedisConnection(): Promise<boolean> {
    const startTime = Date.now();

    try {
      const testKey = 'web3_auth_health_check';
      const testValue = 'ok';

      // 测试写入
      await this.redisClient.set(testKey, testValue, 'EX', 10);

      // 测试读取
      const result = await this.redisClient.get(testKey);

      // 清理测试数据
      await this.redisClient.del(testKey);

      const duration = Date.now() - startTime;
      this.logger.debug(`Redis健康检查完成，耗时: ${duration}ms`);

      return result === testValue;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`Redis连接检查失败，耗时: ${duration}ms`, error);
      return false;
    }
  }

  /**
   * 获取nonce
   * @param getNonceDto 请求参数
   * @returns nonce和签名消息
   * @throws Error 当Redis操作失败时
   */
  async getNonce(getNonceDto: GetNonceDto): Promise<NonceResponseDto> {
    const startTime = Date.now();
    const { walletAddress } = getNonceDto;

    // 验证钱包地址格式（基本验证）
    if (!walletAddress || typeof walletAddress !== 'string') {
      this.logger.warn(`无效的钱包地址格式: ${walletAddress}`);
      throw new Error('Invalid wallet address format');
    }

    // 生成随机nonce（性能优化：直接生成UUID，无需额外处理）
    const nonce = uuidv4();

    try {
      // 将nonce存储到Redis，设置5分钟过期
      // 使用与原版本完全相同的Redis命令格式
      const key = `web3_auth_nonce:${walletAddress.toLowerCase()}`;

      // 性能优化：使用Promise.all并行执行消息生成和Redis存储
      const [, message] = await Promise.all([
        this.redisClient.set(key, nonce, 'EX', 300),
        Promise.resolve(this.generateSignMessage(nonce))
      ]);

      const duration = Date.now() - startTime;
      this.logger.debug(`Nonce生成完成，钱包: ${walletAddress.substring(0, 10)}..., 耗时: ${duration}ms`);

      return {
        nonce,
        message,
      };
    } catch (redisError) {
      const duration = Date.now() - startTime;
      this.logger.error(`Redis存储nonce失败，钱包: ${walletAddress}, 耗时: ${duration}ms`, redisError);
      throw new Error('Failed to store nonce in Redis');
    }
  }

  /**
   * 验证以太坊钱包签名
   * @param message 签名的消息
   * @param signature 签名
   * @param walletAddress 钱包地址
   * @returns 是否验证成功
   */
  private async verifyWeb3Signature(
    message: string,
    signature: string,
    walletAddress: string
  ): Promise<boolean> {
    try {
      // 使用ethers.js验证签名
      const recoveredAddress = ethers.verifyMessage(message, signature);
      // 验证恢复的地址是否与提供的钱包地址匹配（不区分大小写）
      return recoveredAddress.toLowerCase() === walletAddress.toLowerCase();
    } catch (error) {
      this.logger.error('验证签名失败:', error);
      return false;
    }
  }

  /**
   * 生成JWT令牌
   * @param userId 用户ID
   * @param walletId 钱包ID
   * @param walletAddress 钱包地址
   * @returns JWT令牌
   */
  private generateToken(
    userId: number,
    walletId: number,
    walletAddress: string
  ): string {
    const jwtSecret = this.configService.get('JWT_SECRET') ||
                     this.configService.get('app.jwt.secret') ||
                     'your-secret-key';

    return this.jwtService.sign(
      { userId, walletId, walletAddress },
      {
        secret: jwtSecret,
        expiresIn: '60d'
      }
    );
  }

  /**
   * 生成唯一的邀请码
   * @returns 唯一邀请码
   */
  private async getUniqueInviteCode(): Promise<string> {
    const MAX_TRIES = 100;
    for (let i = 0; i < MAX_TRIES; i++) {
      const candidate = generateUniqueCode(6);
      const existing = await UserWallet.findOne({ where: { code: candidate } });
      if (!existing) return candidate;
    }
    throw new Error('Failed to generate unique invite code');
  }

  /**
   * 生成唯一的用户名
   * @returns 唯一用户名
   */
  private async generateUniqueUsernameInternal(): Promise<string> {
    const MAX_TRIES = 100;
    for (let i = 0; i < MAX_TRIES; i++) {
      // 增加更多随机性，包括进程ID和更精确的时间戳
      const timestamp = Date.now();
      const randomPart = Math.random().toString(36).substring(2, 10);
      const processId = process.pid.toString(36);
      const candidate = `user_${timestamp.toString(36)}${randomPart}${processId}`;

      const existing = await User.findOne({ where: { username: candidate } });
      if (!existing) return candidate;
    }
    throw new Error('Failed to generate unique username');
  }

  /**
   * Web3钱包登录
   * @param web3LoginDto 登录参数
   * @returns 包含用户信息和令牌的对象
   */
  async web3Login(web3LoginDto: Web3LoginDto): Promise<Web3LoginResponseDto> {
    const { walletAddress, signature, message, referralCode } = web3LoginDto;
    const startTime = Date.now();

    this.logger.debug(`开始Web3登录，钱包: ${walletAddress.substring(0, 10)}...`);

    // 验证签名 - 统一对所有钱包地址进行签名验证
    const isValidSignature = await this.verifyWeb3Signature(message, signature, walletAddress);
    this.logger.debug(`签名验证完成，钱包: ${walletAddress.substring(0, 10)}..., 结果: ${isValidSignature}`);

    if (!isValidSignature) {
      this.logger.warn(`签名验证失败，钱包: ${walletAddress}`);
      throw new Error('Invalid signature');
    }

    // 查找推荐人钱包（如果提供了推荐码）
    let referrerWallet: UserWallet | null = null;
    let referrerUser: User | null = null;
    if (referralCode) {
      referrerWallet = await UserWallet.findOne({
        where: { code: referralCode }
      });

      if (referrerWallet) {
        referrerUser = await User.findByPk(referrerWallet.userId);
      }

      if (!referrerWallet || !referrerUser) {
        this.logger.warn(`推荐码无效: ${referralCode}`);
        // 不抛出错误，继续登录流程
        referrerWallet = null;
        referrerUser = null;
      }
    }

    // 查找现有钱包
    let userWallet = await UserWallet.findOne({
      where: { parsedWalletAddress: walletAddress.toLowerCase() }
    });

    let user: User;

    if (userWallet) {
      // 现有用户登录
      user = await User.findByPk(userWallet.userId);
      if (!user) {
        throw new Error(`找不到钱包对应的用户，钱包ID: ${userWallet.id}, 用户ID: ${userWallet.userId}`);
      }
      this.logger.debug(`现有用户登录，用户ID: ${user.id}, 钱包: ${walletAddress.substring(0, 10)}...`);
    } else {
      // 新用户注册
      this.logger.debug(`新用户注册，钱包: ${walletAddress.substring(0, 10)}...`);

      // 使用事务确保数据一致性，并添加重试机制处理用户名冲突
      const MAX_RETRIES = 3;
      let result;

      for (let retry = 0; retry < MAX_RETRIES; retry++) {
        try {
          result = await User.sequelize.transaction(async (transaction) => {
            // 在事务内部生成唯一用户名，确保原子性
            const username = await this.generateUniqueUsernameInternal();
            const newUser = await User.create({
              username: username,
              // 使用钱包地址作为 telegramId 的唯一标识符
              telegramId: `web3_${walletAddress.toLowerCase()}`,
              hasFollowedChannel: false,
              authDate: null,
              hash: '',
              telegram_premium: false,
              firstName: '',
              lastName: '',
              photoUrl: '',
              email: null, // 设置为 null 而不是空字符串以避免 email 验证
              referralCount: 0,
              refWalletAddress: referrerWallet?.walletAddress || '',
              referrerId: referrerUser?.id || null
            }, { transaction });

            // 创建用户钱包
            const inviteCode = await this.getUniqueInviteCode();
            const newUserWallet = await UserWallet.create({
              userId: newUser.id,
              walletAddress: walletAddress,
              parsedWalletAddress: walletAddress.toLowerCase(),
              code: inviteCode,
              ton: 0, // 添加 ton 字段，默认值为 0
              gem: 0,
              ticket: 0,
              free_ticket: 0,
              usd: 0,
              moof: 0,
              milk: 0,
              referrerWalletId: referrerWallet?.id
            }, { transaction });

            // 更新用户的firstWalletId
            await newUser.update({ firstWalletId: newUserWallet.id }, { transaction });

            // 如果有推荐人，增加推荐人的推荐计数
            if (referrerUser) {
              await User.increment(
                { referralCount: 1 },
                { where: { id: referrerUser.id }, transaction }
              );
            }

            return { user: newUser, wallet: newUserWallet };
          });

          // 如果成功，跳出重试循环
          break;
        } catch (error) {
          this.logger.warn(`用户创建失败，重试 ${retry + 1}/${MAX_RETRIES}:`, error.message);

          // 如果是最后一次重试，抛出错误
          if (retry === MAX_RETRIES - 1) {
            throw error;
          }

          // 等待一小段时间再重试
          await new Promise(resolve => setTimeout(resolve, 100 * (retry + 1)));
        }
      }

      user = result.user;
      userWallet = result.wallet;
    }

    // 生成JWT令牌
    const token = this.generateToken(user.id, userWallet.id, walletAddress);

    const duration = Date.now() - startTime;
    this.logger.debug(`Web3登录完成，用户ID: ${user.id}, 耗时: ${duration}ms`);

    return {
      token,
      user: {
        id: user.id,
        username: user.username,
        walletAddress: userWallet.walletAddress
      }
    };
  }

  /**
   * 验证nonce是否有效
   * @param walletAddress 钱包地址
   * @param message 签名消息
   * @returns nonce是否有效
   */
  async validateNonce(walletAddress: string, message: string): Promise<boolean> {
    try {
      const key = `web3_auth_nonce:${walletAddress.toLowerCase()}`;
      const storedNonce = await this.redisClient.get(key);

      return storedNonce && message.includes(storedNonce);
    } catch (error) {
      this.logger.error('验证nonce失败:', error);
      return false;
    }
  }

  /**
   * 删除nonce
   * @param walletAddress 钱包地址
   */
  async deleteNonce(walletAddress: string): Promise<void> {
    try {
      const key = `web3_auth_nonce:${walletAddress.toLowerCase()}`;
      await this.redisClient.del(key);
    } catch (error) {
      this.logger.error('删除nonce失败:', error);
    }
  }
}
