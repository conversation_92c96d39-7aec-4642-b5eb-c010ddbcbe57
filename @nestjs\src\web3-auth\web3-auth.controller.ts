import {
  Controller,
  Post,
  Body,
  HttpException,
  HttpStatus,
  Req,
  Get,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import { Request } from 'express';
import { I18nContext } from 'nestjs-i18n';
import { I18nLang } from '../common/decorators';
import { Web3AuthService } from './web3-auth.service';
import { GetNonceDto, Web3LoginDto } from './dto/get-nonce.dto';
import { TranslationService } from '../common/services/translation.service';
import { SkipResponseTransform } from './decorators/skip-response-transform.decorator';
import { SupportedLanguage } from '../i18n/i18n.service';

@Controller('web3-auth')
export class Web3AuthController {
  private readonly logger = new Logger(Web3AuthController.name);

  constructor(
    private readonly web3AuthService: Web3AuthService,
    private readonly translationService: TranslationService,
  ) {}

  /**
   * Redis连接健康检查
   * 路径: /api/web3-auth/health
   * 方法: GET
   */
  @Get('health')
  @SkipResponseTransform()
  async healthCheck() {
    try {
      const isRedisHealthy = await this.web3AuthService.checkRedisConnection();

      return {
        ok: true,
        data: {
          redis: isRedisHealthy ? 'connected' : 'disconnected',
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error: any) {
      throw new HttpException(
        {
          ok: false,
          message: 'Health check failed',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 获取用于签名的nonce
   * 路径: /api/web3-auth/nonce
   * 方法: POST
   */
  @Post('nonce')
  @SkipResponseTransform()
  async getNonce(
    @Body() getNonceDto: GetNonceDto,
    @Req() req: Request,
  ) {
    const startTime = Date.now();
    const clientIP = req.ip || req.socket?.remoteAddress || 'unknown';

    // 手动验证请求参数
    if (!getNonceDto.walletAddress || typeof getNonceDto.walletAddress !== 'string' || getNonceDto.walletAddress.trim() === '') {
      // 获取当前语言上下文
      const i18nContext = I18nContext.current();
      const currentLang = (i18nContext?.lang || 'en') as SupportedLanguage;

      // 获取翻译后的错误消息
      const errorMessage = this.translationService.t(
        'errors.paramValidation',
        {},
        currentLang
      );

      // 抛出与原版本格式完全一致的错误
      throw new BadRequestException({
        ok: false,
        message: errorMessage,
        error: [
          {
            field: 'walletAddress',
            message: this.translationService.t('errors.invalidAddress', {}, currentLang)
          }
        ],
      });
    }

    try {
      this.logger.debug(`Nonce请求开始，钱包: ${getNonceDto.walletAddress.substring(0, 10)}..., IP: ${clientIP}`);

      const result = await this.web3AuthService.getNonce(getNonceDto);

      const duration = Date.now() - startTime;
      this.logger.log(`Nonce请求成功，钱包: ${getNonceDto.walletAddress.substring(0, 10)}..., 耗时: ${duration}ms`);

      // 使用与原版本相同的响应格式
      return {
        ok: true,
        data: result,
      };
    } catch (error: any) {
      const duration = Date.now() - startTime;
      this.logger.error(`获取nonce失败，钱包: ${getNonceDto.walletAddress?.substring(0, 10) || 'unknown'}..., 耗时: ${duration}ms`, error);

      // 根据错误类型返回不同的错误消息
      let errorMessage: string;
      let statusCode = HttpStatus.INTERNAL_SERVER_ERROR;

      if (error.message === 'Invalid wallet address format') {
        errorMessage = this.translationService.tFromRequest(
          req,
          'errors.invalidAddress'
        );
        statusCode = HttpStatus.BAD_REQUEST;
      } else if (error.message === 'Failed to store nonce in Redis') {
        errorMessage = this.translationService.tFromRequest(
          req,
          'errors.serverError'
        );
        statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
      } else {
        errorMessage = this.translationService.tFromRequest(
          req,
          'errors.serverError'
        );
      }

      throw new HttpException(
        {
          ok: false,
          message: errorMessage,
          error: error.message,
        },
        statusCode,
      );
    }
  }

  /**
   * Web3钱包登录
   * 路径: /api/web3-auth/login
   * 方法: POST
   */
  @Post('login')
  @SkipResponseTransform()
  async web3Login(
    @Body() web3LoginDto: Web3LoginDto,
    @Req() req: Request,
    @I18nLang() language: string,
  ) {
    const startTime = Date.now();
    const clientIP = req.ip || req.socket?.remoteAddress || 'unknown';

    try {
      // 验证nonce是否有效
      const isValidNonce = await this.web3AuthService.validateNonce(
        web3LoginDto.walletAddress,
        web3LoginDto.message
      );

      if (!isValidNonce) {
        const errorMessage = this.translationService.t(
          'errors.invalidNonce',
          {},
          language as SupportedLanguage
        );

        return {
          ok: false,
          message: errorMessage,
        };
      }

      // 登录处理
      const result = await this.web3AuthService.web3Login(web3LoginDto);

      // 登录成功后删除nonce
      await this.web3AuthService.deleteNonce(web3LoginDto.walletAddress);

      const duration = Date.now() - startTime;
      this.logger.log(`Web3登录成功，用户ID: ${result.user.id}, 客户端: ${clientIP}, 耗时: ${duration}ms`);

      return {
        ok: true,
        data: {
          token: result.token,
          user: {
            id: result.user.id,
            username: result.user.username,
            walletAddress: result.user.walletAddress
          }
        }
      };
    } catch (error: any) {
      const duration = Date.now() - startTime;
      this.logger.error(`Web3登录失败，钱包: ${web3LoginDto.walletAddress}, 客户端: ${clientIP}, 耗时: ${duration}ms`, error);

      const errorMessage = this.translationService.t(
        'errors.loginFailed',
        {},
        language as SupportedLanguage
      );

      return {
        ok: false,
        message: errorMessage,
        error: error.message
      };
    }
  }
}
